[package]
name = "asi-kafka-consumer"
version = "1.0.0"
edition = "2021"
authors = ["ASI System Team"]
description = "High-performance Kafka consumer for ASI data ingestion"

[dependencies]
# Kafka client
rdkafka = { version = "0.36", features = ["cmake-build", "ssl", "sasl"] }

# gRPC and protobuf
tonic = "0.10"
prost = "0.12"
prost-types = "0.12"

# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-stream = "0.1"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Metrics
prometheus = "0.13"

# Configuration
config = "0.13"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Utilities
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
bytes = "1.0"

# Concurrency
dashmap = "5.0"
arc-swap = "1.0"

# Health checks
hyper = { version = "0.14", features = ["full"] }

[build-dependencies]
tonic-build = "0.10"

[[bin]]
name = "consumer"
path = "src/main.rs"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
debug = true
